package com.ybmmarket20.common;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.gyf.immersionbar.ImmersionBar;
import com.xyy.canary.utils.LogUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.common.eventbus.EventBusUtil;
import com.ybmmarket20.home.IHomeSteadyFragment;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.view.LoadingPage;
import com.ybmmarket20.viewmodel.BaseViewModel;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import butterknife.ButterKnife;

/**
 * Created by asus on 2016/3/10.
 */
public abstract class BaseFragment extends YBMBaseFragment implements IHomeSteadyFragment {

    protected LoadingPage loadingPage;
    private BaseActivity mActivity;
    public boolean isDestroy = false;
    protected BaseFlowData mFlowData; // 埋点数据
    public boolean isKaUser;//是否KA用户
    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof Activity) {
            mActivity = (BaseActivity) context;
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LogUtil.d("fragment", "当前fragment:"+ this.getClass().getSimpleName());
        //初始化是否KA用户
        isKaUser= SpUtil.isKa();
        receiveAnalysisParams();
        if (isRegisterEventBus()) {
            EventBusUtil.register(this);
        }
        setObserver();
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        boolean isCrash = (savedInstanceState != null);
        if (resetIsCrash()) {
            isCrash = false;
        }
        if (!isCrash) {
            loadingPage = new LoadingPage(mActivity) {
                @Override
                protected void onSuccess(ResultState resultState, View successView) {
                    ButterKnife.bind(BaseFragment.this, successView);
                    initTitle();
                    initData(resultState.getContent());
                    initData(resultState.getContent(), savedInstanceState);
                }

                @Override
                protected com.ybmmarket20.common.RequestParams params() {
                    return getParams();
                }

                @Override
                protected String url() {
                    return getUrl();
                }

                @Override
                public int layoutId() {
                    return getLayoutId();
                }
            };
            if (needSetStatusHeight()){
                setStatusHeight(loadingPage);
            }
            return loadingPage;
        } else {
            return new View(mActivity);
        }
    }

    public boolean resetIsCrash() {
        return false;
    }

    //是否需要设置状态栏高度
    protected boolean needSetStatusHeight(){
        return true;
    }
    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        show();
        LogUtil.d("activity", "fragment:"+ this.getClass().getSimpleName());
    }

    protected void initData(String content, Bundle savedInstanceState){

    }

    protected abstract void initData(String content);

    protected abstract void initTitle();

    protected abstract RequestParams getParams();

    protected abstract String getUrl();

    public abstract int getLayoutId();

    @Override
    public void onDestroyView() {
        isDestroy = true;
        super.onDestroyView();
        ButterKnife.unbind(this);
        //unbinder.unbind();//解绑
        if (isRegisterEventBus()) {
            EventBusUtil.unregister(this);
        }
        //Log.e("xyd",this.getClass().getSimpleName() + " onDestoryView");
    }

    @Override
    public void onDestroy() {
        isDestroy = true;
        super.onDestroy();
    }

    public void show() {
        if (loadingPage != null) {
            loadingPage.show();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
        isKaUser= SpUtil.isKa();
    }

    /**
     * 是否注册事件分发
     *
     * @return true绑定EventBus事件分发，默认不绑定，子类需要绑定的话复写此方法返回true.
     */
    protected boolean isRegisterEventBus() {
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventBusCome(Event event) {
        if (event != null) {
            receiveEvent(event);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onStickyEventBusCome(Event event) {
        if (event != null) {
            receiveStickyEvent(event);
        }
    }

    /**
     * 接收到分发到事件
     *
     * @param event 事件
     */
    protected void receiveEvent(Event event) {

    }

    /**
     * 接受到分发的粘性事件
     *
     * @param event 粘性事件
     */
    protected void receiveStickyEvent(Event event) {

    }

    /**
     * 开启带埋点参数的路由
     * @param url
     */
    protected void openUrl(String url){
        FlowDataAnalysisManagerKt.openUrl(url, mFlowData);
    }

    protected void openUrl(String url, BaseFlowData flowData){
        FlowDataAnalysisManagerKt.openUrl(url, flowData);
    }

    /**
     * 接收埋点参数
     */
    protected void receiveAnalysisParams() {
        mFlowData = FlowDataAnalysisManagerKt.generateDefaultBaseFlowData();
        FlowDataAnalysisManagerKt.receiveAnalysisParams(getContext(), mFlowData);
    }

    @Override
    public void getHeaderData() {

    }

    @Override
    public void toTop() {

    }

    public ViewGroup.LayoutParams getRootLayoutParam() {
        return null;
    }

    public void setStatusHeight(View rootView) {
        ViewGroup.LayoutParams lp = getRootLayoutParam();
        if (lp == null) return;
        if (getContext() != null) {
            lp.height = ImmersionBar.getStatusBarHeight((Activity) getContext());
            View statusView = rootView.findViewById(R.id.status_bar);
            statusView.setLayoutParams(lp);
        }
    }

    private void setObserver() {
        BaseViewModel viewModel = getBaseViewModel();
        if (viewModel != null) {
            viewModel.getLoadingLiveData().observe(this, isShow -> {
                if (isShow) {
                    showProgress();
                } else {
                    dismissProgress();
                }
            });
        }
    }

    protected BaseViewModel getBaseViewModel(){
        return null;
    }
}

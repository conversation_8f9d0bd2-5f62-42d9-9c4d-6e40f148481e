package com.ybmmarket20.bean;

import java.util.Objects;

/**
 * 支付配置接口
 */
public class PayTran {
    public String companyInfo;
    public String bankInfo;
    public String bankAccount;
    public String explainOne;
    public String explainTwo;
    public String explainThree;
    public String explainZero;
    public String action;
    public String msg;
    public String isThirdCompany; // 0是自营 1是商家
    public String shopTransferType; // 1-电汇商业;2-电汇平台

    /**
     * 是否是电汇商业
     * @return
     */
    public Boolean isTelegraphicTransferBusiness(){
        return Objects.equals(shopTransferType, "1");
    }
}

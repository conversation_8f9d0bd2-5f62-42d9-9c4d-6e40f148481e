package com.ybmmarket20.activity.afterSales.adapter.detail

import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.adapter.YBMSingleViewAdapter
import com.ybmmarket20.bean.aftersales.AfterSalesDetailStatusBean
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarketkotlin.utils.TimeUtils

/**
 * 售后详情状态信息
 */
class AfterSalesDetailStatusInfoAdapter(
    singleData: AfterSalesDetailStatusBean?
) : YBMSingleViewAdapter<AfterSalesDetailStatusBean>(
    R.layout.item_after_sales_detail_status,
    singleData?: AfterSalesDetailStatusBean()
) {
    override fun bindSingleView(holder: YBMBaseHolder, bean: AfterSalesDetailStatusBean) {
        val tvStatusTitle = holder.getView<TextView>(R.id.tvStatus)
        val llRemainTime = holder.getView<LinearLayout>(R.id.llRemainTime)
        val tvStatusLeftText = holder.getView<TextView>(R.id.tvStatusLeftText)
        val tvStatusMiddleText = holder.getView<TextView>(R.id.tvStatusMiddleText)
        val tvStatusRightText = holder.getView<TextView>(R.id.tvStatusRightText)
        val clStatus = holder.getView<ConstraintLayout>(R.id.clStatus)
        val rtvStatusTips = holder.getView<RoundTextView>(R.id.rtvStatusTips)
        val tvRemainTimeText = holder.getView<TextView>(R.id.tvRemainTimeText)
        tvStatusTitle.text = bean.auditProcessStateName
        rtvStatusTips.text = bean.tips
        rtvStatusTips.visibility = if (bean.tips.isNullOrEmpty()) View.GONE else View.VISIBLE
        llRemainTime.visibility = if (bean.countDownTime > 0) View.VISIBLE else View.GONE
        tvRemainTimeText.text = TimeUtils.timeFormatToMinuteWithUnit(bean.countDownTime * 1000)
        clStatus.visibility = if (bean.auditProcessList.isNullOrEmpty()) View.GONE else {
            try {
                tvStatusLeftText.text = bean.auditProcessList[0].labelTitle
                tvStatusMiddleText.text = bean.auditProcessList[1].labelTitle
                tvStatusRightText.text = bean.auditProcessList[2].labelTitle
            } catch (e: Exception) {
                e.printStackTrace()
            }
            View.VISIBLE
        }
    }
}
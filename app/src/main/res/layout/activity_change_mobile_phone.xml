<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.ybmmarket20.activity.ChangeMobilePhoneActivity">

    <View
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#F5F5F5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_current_phoneNum"
        android:layout_width="match_parent"
        android:layout_height="57dp"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:textColor="@color/color_292933"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@+id/line1"
        tools:text="当前账户：13289072314" />

    <TextView
        android:id="@+id/tv_password_title"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:layout_marginLeft="10dp"
        android:gravity="center_vertical"
        android:text="*密码："
        android:textColor="@color/color_292933"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_current_phoneNum" />

    <com.ybmmarket20.view.XEditText
        android:id="@+id/et_input_password"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginRight="10dp"
        android:background="@null"
        android:drawableRight="@drawable/open"
        android:drawablePadding="15dp"
        android:ellipsize="end"
        android:gravity="center_vertical|right"
        android:hint="请输入当前登录密码"
        android:imeOptions="actionUnspecified"
        android:longClickable="false"
        android:inputType="textPassword"
        android:lines="1"
        android:paddingRight="8dp"
        android:singleLine="true"
        android:textColor="@color/color_292933"
        android:textColorHint="@color/color_9494A6"
        android:textSize="14sp"
        app:layout_constraintLeft_toRightOf="@+id/tv_password_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_password_title" />

    <View
        android:id="@+id/line2"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginRight="10dp"
        android:background="#F5F5F5"
        app:layout_constraintLeft_toLeftOf="@+id/tv_password_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_password_title" />

    <TextView
        android:id="@+id/tv_newPhoneNum_title"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:layout_marginLeft="10dp"
        android:gravity="center_vertical"
        android:text="*认证手机号："
        android:textColor="@color/color_292933"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line2" />

    <EditText
        android:id="@+id/login_et_phoneNum"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginRight="15dp"
        android:background="@null"
        android:gravity="center_vertical|right"
        android:hint="请输入认证手机号"
        android:longClickable="false"
        android:inputType="number"
        android:maxLength="11"
        android:singleLine="true"
        tools:text="13256781234"
        android:textColor="@color/color_292933"
        android:textColorHint="@color/color_9494A6"
        android:textSize="14sp"
        app:layout_constraintLeft_toRightOf="@+id/tv_newPhoneNum_title"
        app:layout_constraintRight_toLeftOf="@+id/login_btn_authCode"
        app:layout_constraintTop_toTopOf="@+id/tv_newPhoneNum_title" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/login_btn_authCode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="10dp"
        android:background="@android:color/transparent"
        android:paddingLeft="12dp"
        android:paddingTop="4dp"
        android:paddingRight="12dp"
        android:paddingBottom="4dp"
        android:text="获取验证码"
        android:textColor="@color/color_00B377"
        android:textSize="14sp"
        app:layout_constraintBottom_toTopOf="@+id/line3"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_newPhoneNum_title"
        app:rv_cornerRadius="2dp"
        app:rv_strokeColor="@color/color_00B377"
        app:rv_strokeWidth="1dp" />

    <View
        android:id="@+id/line3"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginRight="10dp"
        android:background="#F5F5F5"
        app:layout_constraintLeft_toLeftOf="@+id/tv_newPhoneNum_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_newPhoneNum_title" />

    <TextView
        android:id="@+id/tv_verificationCode_title"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:layout_marginLeft="10dp"
        android:gravity="center_vertical"
        android:text="*验证码："
        android:textColor="@color/color_292933"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line3" />

    <EditText
        android:id="@+id/login_et_verificationCode"
        android:layout_width="0dp"
        android:maxLength="10"
        android:layout_height="44dp"
        android:layout_marginRight="10dp"
        android:background="@null"
        android:gravity="center_vertical|right"
        android:hint="请输入"
        android:inputType="number"
        android:longClickable="false"
        android:singleLine="true"
        tools:text="132512321112"
        android:textColor="@color/color_292933"
        android:textColorHint="@color/color_9494A6"
        android:textSize="14sp"
        app:layout_constraintLeft_toRightOf="@+id/tv_verificationCode_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_verificationCode_title" />

    <View
        android:id="@+id/line4"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginRight="10dp"
        android:background="#F5F5F5"
        app:layout_constraintLeft_toLeftOf="@+id/tv_verificationCode_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_verificationCode_title" />

    <Button
        android:id="@+id/tv_submit"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="20dp"
        android:background="@drawable/selector_btn_bg"
        android:gravity="center"
        android:text="提交"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintTop_toBottomOf="@+id/line4" />

</androidx.constraintlayout.widget.ConstraintLayout>

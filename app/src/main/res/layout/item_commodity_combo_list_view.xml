<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="117dp"
    android:layout_height="177dp"
    android:layout_marginStart="10dp"
    android:background="@color/white"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_pic"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="15dp"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="14dp"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="20dp"
        android:textColor="@color/text_292933"
        android:textSize="14sp"
        android:textStyle="bold"
        android:ellipsize="end"
        android:maxLines="1"
        tools:text="珍视明 滴眼液" />

    <TextView
        android:id="@+id/tv_specification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="2dp"
        android:textColor="@color/text_676773"
        android:textSize="12sp"
        android:ellipsize="end"
        android:maxLines="1"
        tools:text="50mg*1盒" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="3dp">

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:textColor="@color/color_FF2121"
            android:textSize="15sp"
            android:textStyle="bold"
            android:ellipsize="end"
            android:maxLines="1"
            tools:text="¥0.00" />

        <TextView
            android:id="@+id/tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:textColor="@color/text_676773"
            android:textSize="10sp"
            android:ellipsize="end"
            android:layout_marginEnd="5dp"
            android:maxLines="1"
            tools:text="X20" />

    </RelativeLayout>

</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_42">

    <TextView
        android:id="@+id/tvAddBankCard"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#676773"
        android:textSize="@dimen/dimen_dp_14"
        android:text="添加银行卡支付"
        android:drawableStart="@drawable/icon_pay_way_add_bank_card"
        android:drawablePadding="@dimen/dimen_dp_6"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginTop="@dimen/dimen_dp_8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_0_5"
        android:background="#F5F5F5"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
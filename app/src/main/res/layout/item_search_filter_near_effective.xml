<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_ybm"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:layout_marginStart="@dimen/dimen_dp_10"
    android:orientation="horizontal">

    <CheckBox
        android:id="@+id/cb_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dimen_dp_42"
        android:background="@drawable/bg_selecter_seach_spacfication"
        android:button="@null"
        android:clickable="false"
        android:textColor="@color/selector_text_color_292933"
        android:gravity="center"
        android:padding="@dimen/dimen_dp_10"
        tools:checked="true"
        android:textSize="@dimen/dimen_dp_12"
        tools:text="15mg*10" />

</LinearLayout>
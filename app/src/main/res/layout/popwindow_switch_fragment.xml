<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/dimen_dp_10"
    android:background="@drawable/bg32"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_switch_fragment"
        android:layout_width="@dimen/dimen_dp_87"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:paddingStart="@dimen/dimen_dp_3"
        android:paddingEnd="@dimen/dimen_dp_3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:itemCount="3"
        tools:listitem="@layout/item_switch_fragment" />

</androidx.constraintlayout.widget.ConstraintLayout>
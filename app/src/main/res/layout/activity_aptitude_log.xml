<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_aptitude_bg"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <View style="@style/normal_horizontal_line" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_document_status"
        android:layout_width="match_parent"
        android:layout_height="91dp"
        android:background="@color/white"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_status_licence_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:includeFontPadding="false"
            android:text="审批状态:"
            android:textColor="@color/color_text_little_base_color"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/receipts_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="12dp"
            android:includeFontPadding="false"
            android:textColor="@color/color_text_status_red"
            android:textSize="14dp"
            app:layout_constraintLeft_toRightOf="@+id/tv_status_licence_title"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="驳回" />

        <TextView
            android:id="@+id/textView8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="单据编号:"
            android:textColor="@color/color_text_little_base_color"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/receipts_status" />

        <TextView
            android:id="@+id/receipts_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="6dp"
            android:textColor="@color/color_text_base_color"
            android:textSize="14dp"
            app:layout_constraintLeft_toRightOf="@+id/textView8"
            app:layout_constraintTop_toBottomOf="@+id/receipts_status"
            tools:text="ddddddd" />

        <TextView
            android:id="@+id/tv_up_time_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="提交时间:"
            android:textColor="@color/color_text_little_base_color"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/receipts_number" />

        <TextView
            android:id="@+id/tv_up_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="6dp"
            android:textColor="@color/color_text_base_color"
            android:textSize="14dp"
            app:layout_constraintLeft_toRightOf="@+id/tv_up_time_hint"
            app:layout_constraintTop_toBottomOf="@+id/receipts_number"
            tools:text="ddddddd" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <View style="@style/normal_horizontal_line" />

    <com.ybmmarket20.common.statusview.StatusViewLayout
        android:id="@+id/svl_examine_log"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/receipts_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:paddingLeft="10dp"
            android:paddingTop="12dp"
            android:paddingRight="10dp"
            android:paddingBottom="12dp" />

    </com.ybmmarket20.common.statusview.StatusViewLayout>

</LinearLayout>
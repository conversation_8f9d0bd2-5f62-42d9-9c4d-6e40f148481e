<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    >
    <RelativeLayout
        android:id="@+id/rel_command"
        android:layout_width="match_parent"
        android:background="@drawable/round_corner_grad_bg"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginTop="21dp"
        android:layout_marginBottom="22dp"
        android:layout_marginRight="15dp"
        android:layout_marginLeft="15dp"
        android:paddingRight="12dp"
        android:paddingLeft="12dp"
        android:paddingBottom="10dp"
        android:paddingTop="10dp"
        android:layout_height="80dp">

        <TextView
            android:id="@+id/tv_command"
            android:maxLines="3"
            android:textSize="14sp"
            android:ellipsize="end"
            android:lineSpacingExtra="3dp"
            android:textColor="@color/text_9494A6"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
        </RelativeLayout>

    <TextView
        android:layout_width="wrap_content"
        android:textSize="14sp"
        android:textColor="@color/text_676773"
        android:layout_marginTop="13dp"
        android:paddingLeft="2dp"
        android:paddingRight="2dp"
        android:background="@color/white"
        android:text="药口令已经为你生成"
        android:layout_centerHorizontal="true"
        android:layout_height="wrap_content" />
    <ImageView
        android:id="@+id/iv_copy"
        android:layout_alignParentRight="true"
        android:layout_marginTop="51dp"
        android:layout_width="110dp"
        android:src="@drawable/ybm_command_copy"
        android:layout_marginRight="5dp"
        android:layout_height="69dp" />
    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="match_parent"
        android:textSize="15sp"
        android:gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:text=""
        android:layout_below="@+id/iv_copy"
        android:textColor="@color/text_292933"
        android:layout_height="wrap_content" />
    <LinearLayout
        android:id="@+id/ll_action"
        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:layout_marginTop="22dp"
        android:gravity="center_horizontal"
        android:layout_below="@+id/tv_tips"
        android:layout_height="wrap_content">
    <TextView
        android:id="@+id/tv_to_wx"
        android:text="去微信粘贴"
        android:drawableTop="@drawable/ybm_action_wx"
        android:drawablePadding="10dp"
        android:layout_width="74dp"
        android:textSize="14sp"
        android:gravity="center"
        android:textColor="@color/text_292933"
        android:layout_height="80dp" />
        <TextView
            android:id="@+id/tv_to_qq"
            android:text="去QQ粘贴"
            android:drawableTop="@drawable/ybm_action_qq"
            android:drawablePadding="10dp"
            android:layout_width="74dp"
            android:textSize="14sp"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:gravity="center"
            android:textColor="@color/text_292933"
            android:layout_height="80dp" />
        <TextView
            android:id="@+id/tv_to_sms"
            android:text="去短信粘贴"
            android:drawableTop="@drawable/ybm_action_sms"
            android:drawablePadding="10dp"
            android:layout_width="74dp"
            android:textSize="14sp"
            android:gravity="center"
            android:textColor="@color/text_292933"
            android:layout_height="80dp" />
    </LinearLayout>
    <Button
        android:id="@+id/btn_cancel"
        android:layout_width="match_parent"
        android:text="取消"
        android:layout_marginRight="20dp"
        android:layout_marginLeft="20dp"
        android:background="@drawable/round_corner_grad_bg"
        android:layout_below="@+id/ll_action"
        android:textSize="15sp"
        android:layout_marginBottom="14dp"
        android:layout_marginTop="14dp"
        android:textColor="@color/text_9494A6"
        android:layout_height="40dp" />
</RelativeLayout>
package com.ybm.app.common;

import android.content.Context;
import android.text.TextUtils;
import android.webkit.WebResourceResponse;

import com.apkfuns.logutils.LogUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ybm.app.bean.CacheEntry;
import com.ybm.app.common.download.DownloadStatus;
import com.ybm.app.common.download.DownloadTask;
import com.ybm.app.utils.SpUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.lang.reflect.Type;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存url文件
 */
public class UrlCacheManager {
    private static UrlCacheManager urlManager;
    public static final long DEFAGE = -1;//默认一直保存
    public static final long KEEP = -1;//一直保存
    public static final long ONE_SECOND = 1000L;
    public static final long ONE_MINUTE = 60L * ONE_SECOND;
    public static final long ONE_HOUR   = 60L * ONE_MINUTE;
    public static final long ONE_DAY    = 24 * ONE_HOUR;
    public static final long ONE_WEEK    = 7 * ONE_DAY;
    private static final String CHARSET    = "UTF-8";
    private int[] supports = new int[]{"js".hashCode(),"css".hashCode(),"png".hashCode(),"jpg".hashCode(),"jpeg".hashCode(),"webp".hashCode(),"ico".hashCode(),"mp3".hashCode(),"apk".hashCode(),"app".hashCode(),"gif".hashCode(),"zip".hashCode()};
//    private int[] supports = new int[]{"html".hashCode(),"htmls".hashCode(),"htm".hashCode(),"js".hashCode(),"css".hashCode(),"png".hashCode(),"jpg".hashCode(),"jpeg".hashCode(),"webp".hashCode(),"ico".hashCode(),"mp3".hashCode(),"apk".hashCode(),"app".hashCode(),"gif".hashCode(),"zip".hashCode()};
    private Map<String, CacheEntry> cacheEntries = null;
    private File rootDir = null;
    private Gson gson;

    public static UrlCacheManager getInstance() {
        if (urlManager == null) {
            synchronized (UrlCacheManager.class) {
                if (urlManager == null) {
                    urlManager = new UrlCacheManager();
                }
            }
        }
        return urlManager;
    }
    private UrlCacheManager() {
        init();
    }
    private void init() {
        if(BaseYBMApp.getApp().isDebug()) {
            rootDir= BaseYBMApp.getAppContext().getExternalCacheDir();
        }else {
            rootDir= BaseYBMApp.getAppContext().getDir("fileCache", Context.MODE_PRIVATE);
        }
        LogUtils.d(rootDir.getAbsolutePath());
        getCacheMap();
        if(cacheEntries == null) {
            cacheEntries = new ConcurrentHashMap<>();
        }
    }

    /**
     *  预计加载功能
     * @param url
     */
    public void preLoad(String url) {
        preLoad(url,DEFAGE);
    }

    /**
     *  预计加载功能
     * @param url
     */
    public void preLoad(String url,long maxAgeMillis) {
        if(!isSupportUrl(url)){
            return;
        }
        if(maxAgeMillis<=0){
            maxAgeMillis = DEFAGE;
        }
        CacheEntry entry = url2Entry(url,maxAgeMillis);
        if(entry == null){
            return;
        }
        preLoad(entry);
    }

    /**
     *  预计加载功能
     * @param cacheEntry
     */
    private void preLoad(final CacheEntry cacheEntry) {
        if(cacheEntry == null){
            return;
        }
        if(hasCache(cacheEntry.url,cacheEntry.maxAgeMillis)){//已经缓存文件了
            LogUtils.d(cacheEntry.fileName +"已经下载完成了，不用重复下载");
            return;
        }
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                File cacheFile = getCacheFile(cacheEntry);
                if(downloadAndStore(cacheEntry,cacheFile)){//下载成功并保存

                }
            }
        });
    }

    //是否包含文件
    private boolean hasCache(String url,long maxAgeMillis){
        CacheEntry cacheEntry = this.cacheEntries.get(url);
        boolean put = false;
        if(cacheEntry == null){//可以查到内存中没有 保存的文件
            cacheEntry = url2Entry(url,maxAgeMillis);
            put = true;
        }
        if(cacheEntry == null){//文件异常，不能下载与缓存
            return false;
        }
        File cachedFile =getCacheFile(cacheEntry);
        if(cachedFile.exists()) {
            long cacheEntryAge = System.currentTimeMillis() - cachedFile.lastModified();
            if (cacheEntry.maxAgeMillis != KEEP && cacheEntryAge > cacheEntry.maxAgeMillis) {//超时
                cachedFile.delete();
                cacheEntries.remove(url);
                put = false;
                return false;
            }
            if (put) {
                cacheEntries.put(url, cacheEntry);
            }
            return true;
        }
        return false;
    }

    private File getCacheFile(String url){
        if(cacheEntries.containsKey(url)){
            return getCacheFile(cacheEntries.get(url));
        }
        CacheEntry entry = url2Entry(url,DEFAGE);
        if(entry == null){
            return null;
        }
        return getCacheFile(entry);
    }
    private File getCacheFile(CacheEntry cacheEntry){
        File cachedFile = new File(this.rootDir.getPath() + File.separator + cacheEntry.dirType+File.separator + cacheEntry.fileName);
        return cachedFile;
    }
    private CacheEntry url2Entry(String url,long maxAgeMillis){
        if(TextUtils.isEmpty(url)|| !url.startsWith("http")){
            return null;
        }
        String fileName = url.substring(url.lastIndexOf("/")+1);
        String extensionName = "";
        if(fileName !=null && fileName.contains(".")) {
             extensionName = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        }
        CacheEntry entry = new CacheEntry(url,fileName,maxAgeMillis);
        setType(entry,extensionName);
        return entry;
    }

    private boolean isSupportUrl(String url){
        if(TextUtils.isEmpty(url) || !url.startsWith("http")){
            return false;
        }
        String fileName = url.substring(url.lastIndexOf("/")+1);
        String extensionName = "";
        if(fileName !=null && fileName.contains(".")) {
            extensionName = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        }else {
            return false;
        }
        if(TextUtils.isEmpty(extensionName)){
            return false;
        }
        int name = extensionName.hashCode();
        for(int str : supports){
            if(name==str){
                return true;
            }
        }
        return false;
    }

    private void setType(CacheEntry entry,String extensionName){
        if(extensionName.equals("html")||extensionName.equals("htm")||extensionName.equals("htms")){
            entry.dirType = "html";
            entry.mimeType = "text/html";
            if(entry.encoding == null){
                entry.encoding = CHARSET;
            }
            return ;
        }else if(extensionName.equals("jpg")||extensionName.equals("jpeg")||extensionName.equals("png")||extensionName.equals("gif")||extensionName.equals("webp")){
            entry.dirType = "img";
            entry.mimeType = "image/"+extensionName;
            if(entry.encoding == null){
                entry.encoding = "";
            }
            return ;
        }else if(extensionName.equals("css")){
            entry.dirType = "css";
            entry.mimeType = "text/css";
            if(entry.encoding == null){
                entry.encoding = CHARSET;
            }
            return ;
        }else if(extensionName.equals("js")){
            entry.dirType = "js";
            entry.mimeType = "application/x-javascript";
            if(entry.encoding == null){
                entry.encoding = CHARSET;
            }
            return ;
        }else if(extensionName.equals("mp3")){
            entry.dirType = "audio";
            entry.mimeType = "audio/mpeg";
            if(entry.encoding == null){
                entry.encoding = "";
            }
            return ;
        }else if(extensionName.equals("zip")){
            entry.dirType = "zip";
            entry.mimeType = "application/zip";
            if(entry.encoding == null){
                entry.encoding = "";
            }
            return ;
        }else if(extensionName.equals("apk")||extensionName.equals("app")){
            entry.dirType = "apk";
            entry.mimeType = "application/apk";
            if(entry.encoding == null){
                entry.encoding = "";
            }
            return ;
        }else if(extensionName.equals("ico")){
            entry.dirType = "img";
            entry.mimeType = "image/x-icon";
            if(entry.encoding == null){
                entry.encoding = "";
            }
            return ;
        }else {
            entry.dirType = "other";
            entry.mimeType = "";
            if(entry.encoding == null){
                entry.encoding = "";
            }
            return ;
        }
    }

    public  File load2File(String url){
        return load2File(url,DEFAGE);
    }
    public synchronized File load2File(String url,long maxAgeMillis){
        if(!isSupportUrl(url)){
            return null;
        }
        if(hasCache(url,maxAgeMillis)){
            return getCacheFile(cacheEntries.get(url));
        } else {
            CacheEntry cacheEntry = url2Entry(url,maxAgeMillis);
            File cachedFile =getCacheFile(cacheEntry);
            if(downloadAndStore(cacheEntry, cachedFile)) {
                return load2File(url);
            }
        }
        return null;
    }

    public  WebResourceResponse load(String url){
        if(TextUtils.isEmpty(url)){
            return null;
        }
        File cachedFile = load2File(url);
        if(cachedFile !=null) {
           CacheEntry cacheEntry = cacheEntries.get(url);
            try {
                return new WebResourceResponse(
                        cacheEntry.mimeType, cacheEntry.encoding, new FileInputStream(cachedFile));
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    //下载文件,并保存在内存中
    private  boolean downloadAndStore(CacheEntry cacheEntry, File cachedFile) {
        DownloadTask task = new DownloadTask(cacheEntry.url.hashCode()+"",cacheEntry.url,cachedFile.getParent()+File.separator,cachedFile.getName()+"_downing");
        task.setAlone(true);
        task.run();
        if(task.downloadStatus ==DownloadStatus.DOWNLOAD_STATUS_COMPLETED){//完成
            File file = new File(task.saveDirPath,task.fileName);
            if(file.exists()){
                file.renameTo(cachedFile);
            }
            cacheEntries.put(cacheEntry.url,cacheEntry);
            LogUtils.d("下载完成:"+cacheEntry.fileName);
            return true;
        }else if(task.downloadStatus ==DownloadStatus.DOWNLOAD_STATUS_ERROR){
            File file = new File(task.saveDirPath,task.fileName);
            if(file.exists()){
                file.delete();
            }
            LogUtils.d("下载失败:"+cacheEntry.fileName);
            return  false;
        }else {
            File file = new File(task.saveDirPath,task.fileName);
            if(file.exists()){
                file.delete();
            }
            LogUtils.d("下载失败" +" url:"+task.url);
            return false;
        }
    }

    private void cacheMap(){
        String json = toJson(cacheEntries,new TypeToken<ConcurrentHashMap<Integer,CacheEntry>>() {
        }.getType());
        SpUtil.writeString("cachefile_key",json);
    }

    private void getCacheMap(){
        String json = SpUtil.readString("cachefile_key",null);
        if(json == null){
            return;
        }
        cacheEntries = json2Obj("cachefile_key",new TypeToken<ConcurrentHashMap<Integer,CacheEntry>>() {}.getType());
    }

    private  String toJson(Object obj,Type type){
        if(gson == null) {
            gson = new Gson();
        }
        try {
            return  gson.toJson(obj,type);
        }catch (Exception e){
            e.printStackTrace();
            return "";
        }
    }

    private <T> T json2Obj(String str,Type type){
        if(gson == null) {
            gson = new Gson();
        }
        try {
            return  gson.fromJson(str,type);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
}

